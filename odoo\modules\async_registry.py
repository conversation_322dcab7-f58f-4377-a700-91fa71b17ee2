# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async-compatible registry extensions for ASGI applications.
This module provides async database connection support for the registry.
"""
from __future__ import annotations

import asyncio
import logging
import threading
import time
from contextlib import asynccontextmanager

from asgiref.sync import sync_to_async, async_to_sync

import odoo
from odoo.modules.registry import Registry
from odoo.async_sql_db import async_db_connect, AsyncTestCursor
from odoo.tools import config

_logger = logging.getLogger(__name__)


class AsyncRegistryMixin:
    """
    Mixin to add async database connection support to Registry.
    This extends the existing Registry class with async capabilities.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Async database connections
        self._async_db = None
        self._async_db_readonly = None
        self._async_db_readonly_failed_time = None

    async def init_async_connections(self):
        """Initialize async database connections"""
        if not hasattr(self, 'db_name'):
            raise ValueError("Registry must be initialized with db_name before async connections")

        # Create async connections
        self._async_db = await async_db_connect(self.db_name, readonly=False)
        
        if config['db_replica_host'] is not False or config['test_enable']:
            self._async_db_readonly = await async_db_connect(self.db_name, readonly=True)

    async def async_cursor(self, readonly=False):
        """
        Return a new async cursor for the database.
        
        :param readonly: Attempt to acquire a cursor on a replica database.
        """
        # Ensure async connections are initialized
        if self._async_db is None:
            await self.init_async_connections()

        # Handle test mode
        if hasattr(self, 'test_cr') and self.test_cr is not None:
            if readonly and not getattr(self, 'test_readonly_enabled', False):
                _logger.info('Explicitly ignoring readonly flag when generating an async cursor')
            return AsyncTestCursor(
                self.test_cr, 
                getattr(self, 'test_lock', threading.RLock()), 
                readonly and getattr(self, 'test_readonly_enabled', False),
                current_test=getattr(odoo.modules.module, 'current_test', None)
            )

        # Handle readonly connections
        if readonly and self._async_db_readonly is not None:
            _REPLICA_RETRY_TIME = 300  # 5 minutes
            if (
                self._async_db_readonly_failed_time is None
                or time.monotonic() > self._async_db_readonly_failed_time + _REPLICA_RETRY_TIME
            ):
                try:
                    cr = self._async_db_readonly.cursor()
                    self._async_db_readonly_failed_time = None
                    return cr
                except Exception:
                    self._async_db_readonly_failed_time = time.monotonic()
                    _logger.warning(
                        "Failed to open an async readonly cursor, falling back to read-write cursor for %dmin %dsec", 
                        *divmod(_REPLICA_RETRY_TIME, 60)
                    )
            threading.current_thread().cursor_mode = 'ro->rw'
        
        return self._async_db.cursor()

    @asynccontextmanager
    async def async_cursor_context(self, readonly=False):
        """Async context manager for database cursors"""
        cursor = await self.async_cursor(readonly=readonly)
        try:
            yield cursor
        finally:
            await cursor.close()

    async def close_async_connections(self):
        """Close async database connections"""
        if self._async_db:
            # Close connection pool
            if hasattr(self._async_db, '_AsyncConnectionPool__pool'):
                await self._async_db._AsyncConnectionPool__pool.close_all()
        
        if self._async_db_readonly:
            if hasattr(self._async_db_readonly, '_AsyncConnectionPool__pool'):
                await self._async_db_readonly._AsyncConnectionPool__pool.close_all()


def patch_registry_for_async():
    """
    Patch the Registry class to support async operations.
    This function adds async methods to the existing Registry class.
    """
    # Add async methods to Registry class
    Registry.init_async_connections = AsyncRegistryMixin.init_async_connections
    Registry.async_cursor = AsyncRegistryMixin.async_cursor
    Registry.async_cursor_context = AsyncRegistryMixin.async_cursor_context
    Registry.close_async_connections = AsyncRegistryMixin.close_async_connections
    
    # Initialize async connection attributes
    original_init = Registry.init
    
    def patched_init(self, db_name):
        original_init(self, db_name)
        # Initialize async connection attributes
        self._async_db = None
        self._async_db_readonly = None
        self._async_db_readonly_failed_time = None
    
    Registry.init = patched_init


class AsyncEnvironment:
    """
    Async-compatible environment wrapper that works with async cursors.
    This provides async database operations while maintaining compatibility.
    """
    
    def __init__(self, cr, uid, context, registry=None):
        self.cr = cr
        self.uid = uid
        self.context = context or {}
        self.registry = registry or Registry(cr.dbname)
        self._cache = {}

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if hasattr(self.cr, 'close'):
            await self.cr.close()

    def __getitem__(self, model_name):
        """Get a model from the environment"""
        if model_name not in self._cache:
            model_class = self.registry[model_name]
            self._cache[model_name] = model_class.with_env(self)
        return self._cache[model_name]

    def __contains__(self, model_name):
        """Check if a model exists in the registry"""
        return model_name in self.registry

    def ref(self, xml_id, raise_if_not_found=True):
        """Get a record by XML ID"""
        return self['ir.model.data']._xmlid_to_res_model_res_id(
            xml_id, raise_if_not_found=raise_if_not_found
        )

    async def flush_all(self):
        """Flush all pending operations"""
        # Convert sync flush to async
        await sync_to_async(lambda: None)()  # Placeholder for actual flush logic

    def with_context(self, **context):
        """Return a new environment with updated context"""
        new_context = dict(self.context, **context)
        return AsyncEnvironment(self.cr, self.uid, new_context, self.registry)

    def with_user(self, user_id):
        """Return a new environment with a different user"""
        return AsyncEnvironment(self.cr, user_id, self.context, self.registry)

    def sudo(self, user_id=None):
        """Return a new environment with superuser privileges"""
        return self.with_user(user_id or odoo.SUPERUSER_ID)


async def create_async_environment(registry, user_id=None, context=None, readonly=False):
    """
    Create an async environment with an async cursor.
    
    :param registry: The registry instance
    :param user_id: User ID for the environment
    :param context: Context dictionary
    :param readonly: Whether to use a readonly cursor
    :return: AsyncEnvironment instance
    """
    if not hasattr(registry, 'async_cursor'):
        patch_registry_for_async()
    
    cursor = await registry.async_cursor(readonly=readonly)
    user_id = user_id or odoo.SUPERUSER_ID
    context = context or {}
    
    return AsyncEnvironment(cursor, user_id, context, registry)


# Utility functions for async/sync compatibility
def ensure_async_registry(registry):
    """Ensure a registry has async capabilities"""
    if not hasattr(registry, 'async_cursor'):
        patch_registry_for_async()
    return registry


async def with_async_cursor(registry, readonly=False):
    """Context manager for async cursors"""
    registry = ensure_async_registry(registry)
    async with registry.async_cursor_context(readonly=readonly) as cursor:
        yield cursor


# Auto-patch Registry when this module is imported
patch_registry_for_async()
