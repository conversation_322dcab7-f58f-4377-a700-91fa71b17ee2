<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="140" viewBox="0 0 120 140">
    <defs>
        <linearGradient id="empty_state_file_happy-c" x1="91.284%" x2="25.707%" y1="100%" y2="0%">
            <stop offset="0%" stop-color="#875A7B"/>
            <stop offset="100%" stop-color="#6B4862"/>
        </linearGradient>
        <circle id="empty_state_file_happy-b" cx="56" cy="76.614" r="56"/>
        <filter id="empty_state_file_happy-a" width="112.5%" height="112.5%" x="-6.2%" y="-4.5%" filterUnits="objectBoundingBox">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="2"/>
            <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.106063179 0"/>
        </filter>
        <path id="empty_state_file_happy-d" d="M56,122.807018 C86.927946,122.807018 112,97.6721263 112,66.6666667 C112,59.6606665 112,37.4384443 112,0 L0,0 C0,23.774138 0,45.9963602 0,66.6666667 C0,97.6721263 25.072054,122.807018 56,122.807018 Z"/>
        <path id="empty_state_file_happy-f" d="M22,6.01503759 L84.3146067,6.01503759 L100.5,22.556391 L100.5,114.829574 L100.5,114.829574 C100.5,124.218415 92.8888407,131.829574 83.5,131.829574 L31,131.829574 L31,131.829574 C21.6111593,131.829574 14,124.218415 14,114.829574 L14,14.0150376 L14,14.0150376 C14,9.5967596 17.581722,6.01503759 22,6.01503759 Z"/>
    </defs>
    <g fill="none" fill-rule="evenodd" transform="translate(4 2)">
        <use fill="#000" filter="url(#empty_state_file_happy-a)" xlink:href="#empty_state_file_happy-b"/>
        <use fill="url(#empty_state_file_happy-c)" xlink:href="#empty_state_file_happy-b"/>
        <g transform="translate(0 10)">
            <mask id="empty_state_file_happy-e" fill="#fff">
                <use xlink:href="#empty_state_file_happy-d"/>
            </mask>
            <g mask="url(#empty_state_file_happy-e)">
                <use fill="#FFF" xlink:href="#empty_state_file_happy-f"/>
                <path stroke="#A1ACBA" stroke-width="2" d="M99.5,22.9642476 L83.8940075,7.01503759 L22,7.01503759 C18.1340068,7.01503759 15,10.1490443 15,14.0150376 L15,114.829574 C15,123.66613 22.163444,130.829574 31,130.829574 L83.5,130.829574 C92.336556,130.829574 99.5,123.66613 99.5,114.829574 L99.5,22.9642476 Z"/>
            </g>
            <path stroke="#A1ACBA" stroke-width="2" d="M84.8002454,27.5045043 C87.4060329,25.0223449 91.4154529,21.2660453 91.7879265,20.6269769 C92.0921888,20.1049414 92.2011803,19.5697888 92.2011803,18.5540391 C92.2011803,17.9213416 92.0599129,17.3140076 91.7659798,16.6778758 C91.2745815,15.6143886 90.3683274,14.4694369 88.765383,12.8381982 C88.2386259,12.302143 86.3189539,10.4445628 84.8002454,8.96835098 L84.8002454,27.5045043 Z" mask="url(#empty_state_file_happy-e)" transform="rotate(135 88.5 18.226)"/>
            <g fill="#000" mask="url(#empty_state_file_happy-e)" opacity=".07">
                <g transform="translate(23 81)">
                    <rect width="46.716" height="6.214"/>
                    <rect width="68.812" height="6.214" y="8.7"/>
                    <rect width="42.297" height="6.214" y="20.506"/>
                    <rect width="52.398" height="6.214" y="31.07"/>
                </g>
            </g>
        </g>
        <path stroke="#A1ACBA" stroke-linecap="round" stroke-width="2" d="M13 14L4 5M18 9L18 0M1 20L10 20"/>
        <g transform="translate(28.491 43.21)">
            <path stroke="#A1ACBA" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.5087719,17.7894737 C28.0087719,26.7894737 44.5087719,17.7894737 44.5087719,17.7894737"/>
            <circle cx="48.009" cy="5.289" r="3.5" fill="#A1ACBA"/>
            <circle cx="8.009" cy="5.289" r="3.5" fill="#A1ACBA"/>
        </g>
    </g>
</svg>
