# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async database service layer for ASGI applications.
This module provides async database operations while maintaining compatibility with existing APIs.
"""
from __future__ import annotations

import asyncio
import base64
import logging
import os
import shutil
import tempfile
from contextlib import asynccontextmanager

from asgiref.sync import sync_to_async, async_to_sync

import odoo
from odoo import SUPERUSER_ID
from odoo.async_sql_db import async_db_connect, connection_info_for
from odoo.exceptions import AccessDenied, UserError
from odoo.modules.async_registry import create_async_environment, ensure_async_registry
from odoo.service.db import (
    check_db_management_enabled, check_super, DatabaseExists,
    _check_faketime_mode, database_identifier
)
from odoo.tools import config
from odoo.tools.sql import SQL

_logger = logging.getLogger(__name__)


class AsyncDatabaseService:
    """
    Async database service that provides async versions of database operations.
    This maintains compatibility with the existing sync API while providing async capabilities.
    """

    @staticmethod
    async def async_db_connect_wrapper(db_name, readonly=False):
        """Async wrapper for database connection"""
        return await async_db_connect(db_name, readonly=readonly)

    @staticmethod
    @asynccontextmanager
    async def async_cursor_context(db_name, readonly=False):
        """Async context manager for database cursors"""
        db = await AsyncDatabaseService.async_db_connect_wrapper(db_name, readonly)
        cursor = db.cursor()
        try:
            yield cursor
        finally:
            await cursor.close()

    @staticmethod
    async def async_create_empty_database(name):
        """Async version of _create_empty_database"""
        async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
            chosen_template = config.get('db_template', 'template1')
            
            # Check if database already exists
            await cr.execute("SELECT datname FROM pg_database WHERE datname = %s", (name,))
            result = await cr.fetchall()
            if result:
                await sync_to_async(_check_faketime_mode)(name)
                raise DatabaseExists(f"database {name!r} already exists!")
            
            # Create database
            await cr.rollback()
            # Note: asyncpg doesn't support autocommit in the same way as psycopg2
            # We'll need to handle this differently
            create_sql = f"CREATE DATABASE {name} ENCODING 'unicode' TEMPLATE {chosen_template}"
            await cr.execute(create_sql)

        # Create extensions
        try:
            async with AsyncDatabaseService.async_cursor_context(name) as cr:
                await cr.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
                if config.get('unaccent'):
                    await cr.execute("CREATE EXTENSION IF NOT EXISTS unaccent")
                    await cr.execute("ALTER FUNCTION unaccent(text) IMMUTABLE")
        except Exception as e:
            _logger.warning("Unable to create PostgreSQL extensions: %s", e)
        
        await sync_to_async(_check_faketime_mode)(name)

        # Restore legacy behavior on pg15+
        try:
            async with AsyncDatabaseService.async_cursor_context(name) as cr:
                await cr.execute("GRANT CREATE ON SCHEMA PUBLIC TO PUBLIC")
        except Exception as e:
            _logger.warning("Unable to make public schema public-accessible: %s", e)

    @staticmethod
    async def async_initialize_db(db_name, demo, lang, user_password, login='admin', country_code=None, phone=None):
        """Async version of _initialize_db"""
        try:
            async with AsyncDatabaseService.async_cursor_context(db_name) as cr:
                # Initialize database modules
                await sync_to_async(odoo.modules.db.initialize)(cr)
                config['load_language'] = lang
                await cr.commit()

            # Create registry and environment
            registry = await sync_to_async(odoo.modules.registry.Registry.new)(db_name)
            registry = ensure_async_registry(registry)
            
            async with registry.async_cursor_context() as cr:
                env = await create_async_environment(registry, SUPERUSER_ID, {})
                
                # Install base modules
                if demo:
                    await sync_to_async(env['ir.module.module'].button_immediate_install)()

                # Update admin user
                values = {'password': user_password, 'lang': lang}
                if login:
                    values['login'] = login
                    emails = odoo.tools.email_split(login)
                    if emails:
                        values['email'] = emails[0]
                
                admin_user = await sync_to_async(env.ref)('base.user_admin')
                await sync_to_async(admin_user.write)(values)
                await cr.commit()

        except Exception as e:
            _logger.exception('Async CREATE DATABASE failed:')
            raise

    @staticmethod
    async def async_create_database(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
        """Async version of exp_create_database"""
        _logger.info('Async create database `%s`.', db_name)
        await AsyncDatabaseService.async_create_empty_database(db_name)
        await AsyncDatabaseService.async_initialize_db(db_name, demo, lang, user_password, login, country_code, phone)
        return True

    @staticmethod
    async def async_drop_database(db_name):
        """Async version of exp_drop"""
        # Check if database exists
        db_list = await sync_to_async(odoo.service.db.list_dbs)(True)
        if db_name not in db_list:
            return False

        # Clean up registry and connections
        await sync_to_async(odoo.modules.registry.Registry.delete)(db_name)
        await sync_to_async(odoo.sql_db.close_db)(db_name)

        async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
            # Drop connections to the database
            await cr.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = %s AND pid <> pg_backend_pid()
            """, (db_name,))

            # Drop the database
            try:
                drop_sql = f"DROP DATABASE {db_name}"
                await cr.execute(drop_sql)
                _logger.info('Async DROP DB: %s', db_name)
            except Exception as e:
                _logger.info('Async DROP DB: %s failed:\n%s', db_name, e)
                raise Exception(f"Couldn't drop database {db_name}: {e}")

        # Remove filestore
        fs = config.filestore(db_name)
        if os.path.exists(fs):
            await sync_to_async(shutil.rmtree)(fs)
        
        return True

    @staticmethod
    async def async_duplicate_database(db_original_name, db_name, neutralize_database=False):
        """Async version of exp_duplicate_database"""
        _logger.info('Async duplicate database `%s` to `%s`.', db_original_name, db_name)
        
        await sync_to_async(odoo.sql_db.close_db)(db_original_name)
        
        async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
            # Drop connections to original database
            await cr.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = %s AND pid <> pg_backend_pid()
            """, (db_original_name,))
            
            # Create new database from template
            create_sql = f"CREATE DATABASE {db_name} ENCODING 'unicode' TEMPLATE {db_original_name}"
            await cr.execute(create_sql)

        # Initialize new registry
        registry = await sync_to_async(odoo.modules.registry.Registry.new)(db_name)
        registry = ensure_async_registry(registry)
        
        async with registry.async_cursor_context() as cr:
            env = await create_async_environment(registry, SUPERUSER_ID, {})
            
            # Force generation of new dbuuid
            config_param = await sync_to_async(env['ir.config_parameter'].init)(force=True)
            
            if neutralize_database:
                await sync_to_async(odoo.modules.neutralize.neutralize_database)(cr)

        # Copy filestore
        from_fs = config.filestore(db_original_name)
        to_fs = config.filestore(db_name)
        if os.path.exists(from_fs) and not os.path.exists(to_fs):
            await sync_to_async(shutil.copytree)(from_fs, to_fs)
        
        return True

    @staticmethod
    async def async_rename_database(old_name, new_name):
        """Async version of exp_rename"""
        await sync_to_async(odoo.modules.registry.Registry.delete)(old_name)
        await sync_to_async(odoo.sql_db.close_db)(old_name)

        async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
            # Drop connections to the database
            await cr.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = %s AND pid <> pg_backend_pid()
            """, (old_name,))

            # Rename the database
            rename_sql = f"ALTER DATABASE {old_name} RENAME TO {new_name}"
            await cr.execute(rename_sql)

        # Move filestore
        old_fs = config.filestore(old_name)
        new_fs = config.filestore(new_name)
        if os.path.exists(old_fs) and not os.path.exists(new_fs):
            await sync_to_async(shutil.move)(old_fs, new_fs)
        
        return True

    @staticmethod
    async def async_database_exists(db_name):
        """Async version of database existence check"""
        try:
            async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
                await cr.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
                result = await cr.fetchone()
                return bool(result)
        except Exception:
            return False

    @staticmethod
    async def async_list_databases():
        """Async version of database listing"""
        try:
            async with AsyncDatabaseService.async_cursor_context('postgres') as cr:
                await cr.execute("""
                    SELECT datname FROM pg_database 
                    WHERE datdba = (SELECT usesysid FROM pg_user WHERE usename = current_user) 
                    AND NOT datistemplate 
                    AND datallowconn 
                    ORDER BY datname
                """)
                result = await cr.fetchall()
                return [row[0] for row in result]
        except Exception:
            return []


# Async wrapper functions for compatibility
async def async_exp_create_database(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    """Async wrapper for database creation"""
    return await AsyncDatabaseService.async_create_database(
        db_name, demo, lang, user_password, login, country_code, phone
    )

async def async_exp_drop(db_name):
    """Async wrapper for database dropping"""
    return await AsyncDatabaseService.async_drop_database(db_name)

async def async_exp_duplicate_database(db_original_name, db_name, neutralize_database=False):
    """Async wrapper for database duplication"""
    return await AsyncDatabaseService.async_duplicate_database(
        db_original_name, db_name, neutralize_database
    )

async def async_exp_rename(old_name, new_name):
    """Async wrapper for database renaming"""
    return await AsyncDatabaseService.async_rename_database(old_name, new_name)

async def async_exp_db_exist(db_name):
    """Async wrapper for database existence check"""
    return await AsyncDatabaseService.async_database_exists(db_name)

async def async_exp_list():
    """Async wrapper for database listing"""
    return await AsyncDatabaseService.async_list_databases()


# Sync wrappers for backward compatibility
def sync_from_async(async_func):
    """Decorator to create sync versions of async functions"""
    def wrapper(*args, **kwargs):
        return async_to_sync(async_func)(*args, **kwargs)
    return wrapper

# Export sync versions for compatibility
exp_create_database_async = sync_from_async(async_exp_create_database)
exp_drop_async = sync_from_async(async_exp_drop)
exp_duplicate_database_async = sync_from_async(async_exp_duplicate_database)
exp_rename_async = sync_from_async(async_exp_rename)
exp_db_exist_async = sync_from_async(async_exp_db_exist)
exp_list_async = sync_from_async(async_exp_list)
