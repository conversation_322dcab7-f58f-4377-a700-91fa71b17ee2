# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Odoo ASGI application and async HTTP handling.
This module provides ASGI-compatible request handling to replace the WSGI implementation.
"""
from __future__ import annotations

import asyncio
import inspect
import json
import logging
import os
import threading
import time
from contextlib import asynccontextmanager
from typing import Dict, List, Tuple, Any, Callable, Awaitable

from asgiref.compatibility import guarantee_single_callable
from asgiref.sync import sync_to_async, async_to_sync
import werkzeug
from werkzeug.exceptions import HTTPException, NotFound, BadRequest, InternalServerError

import odoo
from odoo import http
from odoo.http import Request, Response, _request_stack
from odoo.tools import config
from odoo.tools.misc import get_manifest

_logger = logging.getLogger(__name__)

# ASGI scope types
ASGIScope = Dict[str, Any]
ASGIReceive = Callable[[], Awaitable[Dict[str, Any]]]
ASGISend = Callable[[Dict[str, Any]], Awaitable[None]]
ASGIApp = Callable[[ASGIScope, ASGIReceive, ASGISend], Awaitable[None]]


class ASGIRequest(Request):
    """
    ASGI-compatible request wrapper that extends the base Request class
    to work with async operations.
    """

    def __init__(self, scope: ASGIScope, receive: ASGIReceive, send: ASGISend):
        self.scope = scope
        self.receive = receive
        self.send = send

        # Create a werkzeug request from ASGI scope
        httprequest = self._create_werkzeug_request(scope)
        super().__init__(httprequest)

        # ASGI-specific attributes
        self._body_consumed = False
        self._body_cache = None

    def _create_werkzeug_request(self, scope: ASGIScope) -> werkzeug.Request:
        """Convert ASGI scope to werkzeug Request"""
        # Extract HTTP information from ASGI scope
        method = scope['method']
        path = scope['path']
        query_string = scope.get('query_string', b'').decode('latin1')
        headers = dict(scope.get('headers', []))
        
        # Convert headers to werkzeug format
        environ = {
            'REQUEST_METHOD': method,
            'PATH_INFO': path,
            'QUERY_STRING': query_string,
            'CONTENT_TYPE': headers.get(b'content-type', b'').decode('latin1'),
            'CONTENT_LENGTH': headers.get(b'content-length', b'').decode('latin1'),
            'SERVER_NAME': scope.get('server', ['localhost', None])[0],
            'SERVER_PORT': str(scope.get('server', [None, 80])[1]),
            'wsgi.version': (1, 0),
            'wsgi.url_scheme': scope.get('scheme', 'http'),
            'wsgi.input': None,  # Will be handled separately for ASGI
            'wsgi.errors': None,
            'wsgi.multithread': True,
            'wsgi.multiprocess': False,
            'wsgi.run_once': False,
        }
        
        # Add HTTP headers to environ
        for name, value in headers.items():
            name = name.decode('latin1')
            value = value.decode('latin1')
            
            # Convert to CGI-style header name
            if name.lower() == 'content-type':
                environ['CONTENT_TYPE'] = value
            elif name.lower() == 'content-length':
                environ['CONTENT_LENGTH'] = value
            else:
                key = f'HTTP_{name.upper().replace("-", "_")}'
                environ[key] = value
        
        # Add client information
        if 'client' in scope:
            environ['REMOTE_ADDR'] = scope['client'][0]
            environ['REMOTE_PORT'] = str(scope['client'][1])
        
        return werkzeug.Request(environ)

    async def get_body(self) -> bytes:
        """Get the request body asynchronously"""
        if self._body_consumed:
            return self._body_cache or b''
        
        body_parts = []
        while True:
            message = await self.receive()
            if message['type'] == 'http.request':
                body_parts.append(message.get('body', b''))
                if not message.get('more_body', False):
                    break
            elif message['type'] == 'http.disconnect':
                break
        
        self._body_cache = b''.join(body_parts)
        self._body_consumed = True
        return self._body_cache

    async def _serve_static_async(self):
        """Async version of static file serving"""
        # Convert sync static serving to async
        return await sync_to_async(self._serve_static)()

    async def _serve_nodb_async(self):
        """Async version of no-database request serving"""
        return await sync_to_async(self._serve_nodb)()

    async def _serve_db_async(self):
        """Async version of database request serving"""
        return await sync_to_async(self._serve_db)()

    async def get_json_data(self):
        """Get JSON data from request body asynchronously"""
        body = await self.get_body()
        if not body:
            return {}

        try:
            return json.loads(body.decode('utf-8'))
        except (ValueError, UnicodeDecodeError) as e:
            raise ValueError(f"Invalid JSON data: {e}")

    def make_json_response(self, data, headers=None):
        """Create a JSON response"""
        import json
        response_data = json.dumps(data, default=http.json_default)
        response = Response(response_data, content_type='application/json')
        if headers:
            for key, value in headers.items():
                response.headers[key] = value
        return response


class ASGIApplication:
    """
    Odoo ASGI application that replaces the WSGI Application class.
    Provides async request handling while maintaining compatibility with existing code.
    """

    def __init__(self):
        self._static_cache = None

    @property
    def statics(self):
        """
        Map module names to their absolute static path on the file system.
        Cached property for performance.
        """
        if self._static_cache is None:
            mod2path = {}
            for addons_path in odoo.addons.__path__:
                for module in os.listdir(addons_path):
                    manifest = get_manifest(module)
                    static_path = os.path.join(addons_path, module, 'static')
                    if (manifest
                            and (manifest['installable'] or manifest['assets'])
                            and os.path.isdir(static_path)):
                        mod2path[module] = static_path
            self._static_cache = mod2path
        return self._static_cache

    def get_static_file(self, path: str) -> str:
        """
        Check if the given path corresponds to a static file and return its filesystem path.
        """
        if not path.startswith('/'):
            return None
            
        path_parts = path.strip('/').split('/')
        if len(path_parts) < 3 or path_parts[1] != 'static':
            return None
            
        module = path_parts[0]
        if module in self.statics:
            file_path = os.path.join(self.statics[module], *path_parts[2:])
            if os.path.isfile(file_path):
                return file_path
        return None

    def set_csp(self, response):
        """Set Content Security Policy headers"""
        # Implementation similar to the original but adapted for ASGI
        pass

    async def __call__(self, scope: ASGIScope, receive: ASGIReceive, send: ASGISend) -> None:
        """
        ASGI application entry point.
        
        :param scope: ASGI scope containing request information
        :param receive: ASGI receive callable for getting request body
        :param send: ASGI send callable for sending response
        """
        if scope['type'] != 'http':
            # Only handle HTTP requests
            await send({
                'type': 'http.response.start',
                'status': 404,
                'headers': [[b'content-type', b'text/plain']],
            })
            await send({
                'type': 'http.response.body',
                'body': b'Not Found',
            })
            return

        # Initialize thread-local variables
        current_thread = threading.current_thread()
        current_thread.query_count = 0
        current_thread.query_time = 0
        current_thread.perf_t0 = time.time()
        current_thread.cursor_mode = None
        
        # Clean up thread attributes
        for attr in ('dbname', 'uid'):
            if hasattr(current_thread, attr):
                delattr(current_thread, attr)

        try:
            # Create ASGI request
            request = ASGIRequest(scope, receive, send)
            _request_stack.append(request)
            
            # Initialize request
            request._post_init()

            # Route the request
            response = await self._route_request(request)
            
            # Send the response
            await self._send_response(response, send)

        except Exception as exc:
            # Handle exceptions
            await self._handle_exception(exc, request, send)
        finally:
            # Clean up
            if _request_stack:
                _request_stack.pop()

    async def _route_request(self, request: ASGIRequest) -> Response:
        """Route the request to appropriate handler"""
        path = request.httprequest.path
        
        # Check for static files first
        if self.get_static_file(path):
            return await request._serve_static_async()
        
        # Check if database is available
        elif request.db:
            try:
                # Use profiler context if available
                with request._get_profiler_context_manager():
                    return await request._serve_db_async()
            except Exception as e:
                _logger.warning("Database or registry unusable, trying without", exc_info=e)
                request.db = None
                request.session.logout()
                
                # Handle specific protected routes
                if (path.startswith('/odoo/') or 
                    path in ('/odoo', '/web', '/web/login', '/test_http/ensure_db')):
                    # Remove ?db= from query string
                    args_nodb = request.httprequest.args.copy()
                    args_nodb.pop('db', None)
                    from werkzeug.urls import url_encode
                    request.reroute(path, url_encode(args_nodb))
                
                return await request._serve_nodb_async()
        else:
            return await request._serve_nodb_async()

    async def _send_response(self, response: Response, send: ASGISend) -> None:
        """Send HTTP response via ASGI"""
        # Convert werkzeug response to ASGI format
        status = response.status_code
        headers = []
        
        for key, value in response.headers:
            headers.append([key.encode(), value.encode()])

        # Send response start
        await send({
            'type': 'http.response.start',
            'status': status,
            'headers': headers,
        })

        # Send response body
        if hasattr(response, 'iter_encoded'):
            # Stream response
            for chunk in response.iter_encoded():
                if chunk:
                    await send({
                        'type': 'http.response.body',
                        'body': chunk,
                        'more_body': True,
                    })
            # Send final empty chunk
            await send({
                'type': 'http.response.body',
                'body': b'',
                'more_body': False,
            })
        else:
            # Single response body
            body = response.get_data()
            await send({
                'type': 'http.response.body',
                'body': body,
            })

    async def _handle_exception(self, exc: Exception, request: ASGIRequest, send: ASGISend) -> None:
        """Handle exceptions and send error response"""
        try:
            # Log the error
            if hasattr(exc, 'loglevel'):
                _logger.log(exc.loglevel, exc, exc_info=getattr(exc, 'exc_info', None))
            elif isinstance(exc, HTTPException):
                pass
            elif isinstance(exc, http.SessionExpiredException):
                _logger.info(exc)
            elif isinstance(exc, (odoo.exceptions.UserError, odoo.exceptions.AccessError)):
                _logger.warning(exc)
            else:
                _logger.error("Exception during request handling.", exc_info=True)

            # Get error response
            if hasattr(exc, 'error_response'):
                error_response = exc.error_response
            else:
                error_response = request.dispatcher.handle_error(exc)

            # Send error response
            await self._send_response(error_response, send)
            
        except Exception as send_exc:
            # Fallback error handling
            _logger.error("Failed to send error response", exc_info=True)
            try:
                await send({
                    'type': 'http.response.start',
                    'status': 500,
                    'headers': [[b'content-type', b'text/plain']],
                })
                await send({
                    'type': 'http.response.body',
                    'body': b'Internal Server Error',
                })
            except Exception:
                # If we can't even send a basic error response, log and give up
                _logger.error("Failed to send fallback error response", exc_info=True)


class AsyncDispatcher:
    """
    Async-compatible dispatcher that can handle both sync and async endpoints.
    """

    def __init__(self, request):
        self.request = request

    async def dispatch_async(self, endpoint, args):
        """
        Dispatch to an endpoint, handling both sync and async functions.
        """
        # Check if endpoint is async
        if inspect.iscoroutinefunction(endpoint):
            # Async endpoint - call directly
            return await endpoint(**args)
        else:
            # Sync endpoint - run in thread pool
            return await sync_to_async(endpoint)(**args)

    async def handle_error_async(self, exc):
        """Handle errors in async context"""
        # Convert sync error handling to async
        return await sync_to_async(self.request.dispatcher.handle_error)(exc)


def async_route(route=None, **routing):
    """
    Async-compatible route decorator that works with both sync and async endpoints.
    This extends the regular @route decorator to support async functions.
    """
    def decorator(endpoint):
        # Apply the regular route decorator first
        wrapped_endpoint = http.route(route, **routing)(endpoint)

        # Mark as async-compatible
        wrapped_endpoint._is_async_compatible = True

        return wrapped_endpoint

    return decorator


class AsyncHttpDispatcher:
    """
    Async version of HttpDispatcher that can handle async endpoints.
    """

    routing_type = 'http'

    def __init__(self, request):
        self.request = request

    @classmethod
    def is_compatible_with(cls, request):
        return True

    async def dispatch_async(self, endpoint, args):
        """
        Async version of HTTP dispatch that handles CSRF, parameters, etc.
        """
        # Set up request parameters
        http_params = await sync_to_async(self.request.get_http_params)()
        self.request.params = dict(http_params, **args)

        # CSRF validation for non-safe methods
        if (self.request.httprequest.method not in http.CSRF_FREE_METHODS and
            getattr(endpoint, 'routing', {}).get('csrf', True)):

            if not self.request.db:
                return self.request.redirect('/web/database/selector')

            token = self.request.params.pop('csrf_token', None)
            if not await sync_to_async(self.request.validate_csrf)(token):
                if token is not None:
                    _logger.warning("CSRF validation failed on path '%s'",
                                  self.request.httprequest.path)
                else:
                    _logger.warning("Missing CSRF token on path '%s'",
                                  self.request.httprequest.path)
                raise BadRequest('Session expired (invalid CSRF token)')

        # Dispatch to endpoint
        if self.request.db:
            # Use ir.http for database requests
            return await sync_to_async(self.request.registry['ir.http']._dispatch)(endpoint)
        else:
            # Direct endpoint call for no-database requests
            if inspect.iscoroutinefunction(endpoint):
                return await endpoint(**self.request.params)
            else:
                return await sync_to_async(endpoint)(**self.request.params)

    async def post_dispatch_async(self, response):
        """Async version of post-dispatch processing"""
        await sync_to_async(self.request._save_session)()
        await sync_to_async(self.request._inject_future_response)(response)
        # CSP headers would be set here


class AsyncJsonRPCDispatcher:
    """
    Async version of JsonRPCDispatcher for JSON-RPC requests.
    """

    routing_type = 'json'

    def __init__(self, request):
        self.request = request
        self.jsonrequest = {}
        self.request_id = None

    @classmethod
    def is_compatible_with(cls, request):
        return request.httprequest.mimetype in http.JSON_MIMETYPES

    async def dispatch_async(self, endpoint, args):
        """
        Async JSON-RPC dispatch handling.
        """
        try:
            self.jsonrequest = await self.request.get_json_data()
            self.request_id = self.jsonrequest.get('id')
        except ValueError:
            raise BadRequest("Invalid JSON data")

        self.request.params = dict(self.jsonrequest.get('params', {}), **args)

        if self.request.db:
            result = await sync_to_async(self.request.registry['ir.http']._dispatch)(endpoint)
        else:
            if inspect.iscoroutinefunction(endpoint):
                result = await endpoint(**self.request.params)
            else:
                result = await sync_to_async(endpoint)(**self.request.params)

        return self._response(result)

    def _response(self, result=None, error=None):
        """Create JSON-RPC response"""
        response = {'jsonrpc': '2.0', 'id': self.request_id}
        if error is not None:
            response['error'] = error
        if result is not None:
            response['result'] = result

        return self.request.make_json_response(response)


# Global ASGI application instance
asgi_root = ASGIApplication()
