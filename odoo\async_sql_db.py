# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async PostgreSQL connector for ASGI applications.
This module provides async database connectivity using asyncpg,
replacing the synchronous psycopg2-based implementation.
"""
from __future__ import annotations

import asyncio
import logging
import os
import re
import threading
import time
import typing
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timed<PERSON><PERSON>
from inspect import currentframe

import asyncpg
from asgiref.sync import async_to_sync, sync_to_async
from werkzeug import urls

import odoo
from odoo.tools import config, frame_codeinfo, reverse_enumerate

_logger = logging.getLogger(__name__)
_logger_conn = logging.getLogger(__name__ + '.connection')

# Global SQL counter for statistics
sql_counter = 0

# Maximum idle timeout for connections (in seconds)
MAX_IDLE_TIMEOUT = 600  # 10 minutes

# Exception classes for compatibility
class PoolError(Exception):
    """Exception raised when connection pool is full or has issues"""
    pass

class InterfaceError(Exception):
    """Exception raised for interface-related errors"""
    pass

# SQL query wrapper for compatibility
class SQL:
    def __init__(self, code, params=None):
        self.code = code
        self.params = params or ()

# Callbacks management
class Callbacks:
    def __init__(self):
        self._callbacks = []

    def add(self, callback):
        self._callbacks.append(callback)

    def run(self):
        for callback in self._callbacks:
            try:
                callback()
            except Exception:
                _logger.exception("Error in callback")

    def clear(self):
        self._callbacks.clear()

# Savepoint management
class Savepoint:
    def __init__(self, cursor):
        self.cursor = cursor
        self.name = f"sp_{uuid.uuid4().hex[:8]}"
        self._closed = False

    async def __aenter__(self):
        await self.cursor.execute(f"SAVEPOINT {self.name}")
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        if not self._closed:
            if exc_type is None:
                await self.cursor.execute(f"RELEASE SAVEPOINT {self.name}")
            else:
                await self.cursor.execute(f"ROLLBACK TO SAVEPOINT {self.name}")
            self._closed = True

    async def close(self, rollback=False):
        if not self._closed:
            if rollback:
                await self.cursor.execute(f"ROLLBACK TO SAVEPOINT {self.name}")
            else:
                await self.cursor.execute(f"RELEASE SAVEPOINT {self.name}")
            self._closed = True

class _FlushingSavepoint(Savepoint):
    async def __aenter__(self):
        self.cursor.flush()
        return await super().__aenter__()

    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            return await super().__aexit__(exc_type, exc_value, traceback)
        finally:
            if exc_type is None:
                self.cursor.flush()
            else:
                self.cursor.clear()

class BaseAsyncCursor:
    """Base class for async cursors that manage pre/post commit hooks."""

    def __init__(self):
        self.precommit = Callbacks()
        self.postcommit = Callbacks()
        self.prerollback = Callbacks()
        self.postrollback = Callbacks()
        self.transaction = None

    def flush(self):
        """Flush the current transaction, and run precommit hooks."""
        if self.transaction is not None:
            self.transaction.flush()
        self.precommit.run()

    def clear(self):
        """Clear the current transaction, and clear precommit hooks."""
        if self.transaction is not None:
            self.transaction.clear()
        self.precommit.clear()

    def reset(self):
        """Reset the current transaction (this invalidates more than clear())."""
        if self.transaction is not None:
            self.transaction.reset()

    def savepoint(self, flush=True) -> Savepoint:
        """Context manager entering in a new savepoint"""
        if flush:
            return _FlushingSavepoint(self)
        else:
            return Savepoint(self)

    async def __aenter__(self):
        """Using the cursor as an async context manager automatically commits and closes it"""
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            if exc_type is None:
                await self.commit()
        finally:
            await self.close()

class AsyncCursor(BaseAsyncCursor):
    """
    Async cursor for PostgreSQL database operations using asyncpg.
    This replaces the psycopg2-based Cursor class.
    """

    def __init__(self, pool, dbname, dsn):
        super().__init__()
        self.__pool = pool
        self.dbname = dbname
        self.dsn = dsn
        self.sql_log_count = 0
        
        # Connection and cursor will be set when acquired
        self._cnx = None
        self._obj = None
        self._closed = False
        
        # Debug information
        if _logger.isEnabledFor(logging.DEBUG):
            self.__caller = frame_codeinfo(currentframe(), 2)
        else:
            self.__caller = False
            
        self.cache = {}
        self._now = None

    async def _acquire_connection(self):
        """Acquire a connection from the pool"""
        if self._cnx is None:
            self._cnx = await self.__pool.borrow(self.dsn)
            # Set transaction isolation level
            await self._cnx.execute("SET TRANSACTION ISOLATION LEVEL REPEATABLE READ")
            if self.__pool.readonly:
                await self._cnx.execute("SET TRANSACTION READ ONLY")
            
            # Set search path for non-postgres databases in test mode
            if self.dbname != 'postgres' and os.getenv('ODOO_FAKETIME_TEST_MODE'):
                await self._cnx.execute("SET search_path = public, pg_catalog")

    def __build_dict(self, row, description):
        """Build a dictionary from a row and column description"""
        return {desc['name']: row[i] for i, desc in enumerate(description)}

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a SQL query"""
        global sql_counter

        if isinstance(query, SQL):
            assert params is None, "Unexpected parameters for SQL query object"
            query, params = query.code, query.params

        if params and not isinstance(params, (tuple, list, dict)):
            raise ValueError("SQL query parameters should be a tuple, list or dict; got %r" % (params,))

        await self._acquire_connection()

        start = time.time()
        try:
            params = params or ()
            # Convert psycopg2-style parameters to asyncpg format
            if isinstance(params, dict):
                # Named parameters - convert to positional
                formatted_query = query
                param_values = []
                for key, value in params.items():
                    formatted_query = formatted_query.replace(f'%({key})s', f'${len(param_values) + 1}')
                    param_values.append(value)
                self._last_result = await self._cnx.execute(formatted_query, *param_values)
            else:
                # Positional parameters
                if params:
                    # Convert %s to $1, $2, etc.
                    param_count = 0
                    formatted_query = ""
                    i = 0
                    while i < len(query):
                        if query[i:i+2] == '%s':
                            param_count += 1
                            formatted_query += f'${param_count}'
                            i += 2
                        else:
                            formatted_query += query[i]
                            i += 1
                    self._last_result = await self._cnx.execute(formatted_query, *params)
                else:
                    self._last_result = await self._cnx.execute(query)
        except Exception as e:
            if log_exceptions:
                _logger.error("bad query: %s\nERROR: %s", query, e)
            raise
        finally:
            delay = time.time() - start
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug("[%.3f ms] query: %s", 1000 * delay, query)

        # Update counters
        self.sql_log_count += 1
        sql_counter += 1

        current_thread = threading.current_thread()
        if hasattr(current_thread, 'query_count'):
            current_thread.query_count += 1
            current_thread.query_time += delay

        # Optional hooks for performance and tracing analysis
        for hook in getattr(current_thread, 'query_hooks', ()):
            hook(self, query, params, start, delay)

        return self._last_result

    async def fetch(self, query, params=None, log_exceptions=True):
        """Execute a query and return all results"""
        await self._acquire_connection()

        start = time.time()
        try:
            params = params or ()
            # Convert psycopg2-style parameters to asyncpg format
            if isinstance(params, dict):
                formatted_query = query
                param_values = []
                for key, value in params.items():
                    formatted_query = formatted_query.replace(f'%({key})s', f'${len(param_values) + 1}')
                    param_values.append(value)
                result = await self._cnx.fetch(formatted_query, *param_values)
            else:
                if params:
                    # Convert %s to $1, $2, etc.
                    param_count = 0
                    formatted_query = ""
                    i = 0
                    while i < len(query):
                        if query[i:i+2] == '%s':
                            param_count += 1
                            formatted_query += f'${param_count}'
                            i += 2
                        else:
                            formatted_query += query[i]
                            i += 1
                    result = await self._cnx.fetch(formatted_query, *params)
                else:
                    result = await self._cnx.fetch(query)

            # Store result for fetchone/fetchall compatibility
            self._last_fetch_result = list(result)
            self._fetch_index = 0
            return result

        except Exception as e:
            if log_exceptions:
                _logger.error("bad query: %s\nERROR: %s", query, e)
            raise
        finally:
            delay = time.time() - start
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug("[%.3f ms] fetch query: %s", 1000 * delay, query)

    async def fetchone(self):
        """Fetch one row from the last query result"""
        if not hasattr(self, '_last_fetch_result') or not self._last_fetch_result:
            return None

        if self._fetch_index < len(self._last_fetch_result):
            row = self._last_fetch_result[self._fetch_index]
            self._fetch_index += 1
            return row
        return None

    async def fetchall(self):
        """Fetch all rows from the last query result"""
        if not hasattr(self, '_last_fetch_result'):
            return []

        remaining = self._last_fetch_result[self._fetch_index:]
        self._fetch_index = len(self._last_fetch_result)
        return remaining

    def fetchone_sync(self):
        """Synchronous version of fetchone for compatibility"""
        return async_to_sync(self.fetchone)()

    def fetchall_sync(self):
        """Synchronous version of fetchall for compatibility"""
        return async_to_sync(self.fetchall)()

    def mogrify(self, query, params=None):
        """Format a query with parameters (for compatibility)"""
        if isinstance(query, SQL):
            assert params is None, "Unexpected parameters for SQL query object"
            query, params = query.code, query.params

        # Simple parameter substitution for debugging
        if params:
            if isinstance(params, dict):
                for key, value in params.items():
                    query = query.replace(f'%({key})s', repr(value))
            else:
                for param in params:
                    query = query.replace('%s', repr(param), 1)

        return query.encode('utf-8')

    async def commit(self):
        """Perform an SQL COMMIT"""
        self.flush()
        if self._cnx:
            await self._cnx.execute("COMMIT")
        self.clear()
        self._now = None
        self.prerollback.clear()
        self.postrollback.clear()
        self.postcommit.run()

    async def rollback(self):
        """Perform an SQL ROLLBACK"""
        self.clear()
        self.postcommit.clear()
        self.prerollback.run()
        if self._cnx:
            await self._cnx.execute("ROLLBACK")
        self._now = None
        self.postrollback.run()

    async def close(self, leak=False):
        """Close the cursor and return connection to pool"""
        if self._closed:
            return

        self._closed = True
        if self._cnx:
            if leak:
                # Mark connection as leaked
                setattr(self._cnx, 'leaked', True)
            else:
                chosen_template = config.get('db_template', 'template1')
                keep_in_pool = self.dbname not in ('template0', 'template1', 'postgres', chosen_template)
                await self.__pool.give_back(self._cnx, keep_in_pool=keep_in_pool)

    @property
    def closed(self):
        return self._closed or (self._cnx and self._cnx.is_closed())

    @property
    def readonly(self):
        return bool(self.__pool.readonly)

    async def now(self):
        """Return the transaction's timestamp NOW() AT TIME ZONE 'UTC'"""
        if self._now is None:
            await self.execute("SELECT (now() AT TIME ZONE 'UTC')")
            result = await self.fetchone()
            self._now = result[0]
        return self._now

    def __del__(self):
        if not self._closed and self._cnx and not self._cnx.is_closed():
            msg = "Async cursor not closed explicitly\n"
            if self.__caller:
                msg += "Cursor was created at %s:%s" % self.__caller
            else:
                msg += "Please enable sql debugging to trace the caller."
            _logger.warning(msg)
            # Note: We can't call async close() from __del__, so we mark it as leaked
            if self._cnx:
                setattr(self._cnx, 'leaked', True)


class AsyncTestCursor(BaseAsyncCursor):
    """
    Async version of TestCursor for testing purposes.
    Maintains transaction state across requests for testing.
    """

    _cursors_stack = []

    def __init__(self, cursor, lock, readonly, current_test=None):
        assert isinstance(cursor, BaseAsyncCursor)
        self.current_test = current_test
        super().__init__()
        self._now = None
        self._closed = False
        self._cursor = cursor
        self.readonly = readonly
        self._lock = lock
        self._savepoint = None

        # Acquire lock and manage cursor stack
        self._lock.acquire()
        last_cursor = self._cursors_stack and self._cursors_stack[-1]
        if last_cursor and last_cursor.readonly and not readonly and last_cursor._savepoint:
            raise Exception('Opening a read/write test cursor from a readonly one')
        self._cursors_stack.append(self)

    async def close(self):
        """Close the test cursor"""
        if not self._closed:
            try:
                await self.rollback()
                if self._savepoint:
                    await self._savepoint.close(rollback=False)
            finally:
                self._closed = True
                tos = self._cursors_stack.pop()
                if tos is not self:
                    _logger.warning("Found different un-closed cursor when trying to close %s: %s", self, tos)
                self._lock.release()

    async def commit(self):
        """Perform a test commit (using savepoints)"""
        self.flush()
        if self._savepoint:
            await self._savepoint.close(rollback=self.readonly)
            self._savepoint = None
        self.clear()
        self.prerollback.clear()
        self.postrollback.clear()
        self.postcommit.clear()  # TestCursor ignores post-commit hooks by default

    async def rollback(self):
        """Perform a test rollback"""
        self.clear()
        self.postcommit.clear()
        self.prerollback.run()
        if self._savepoint:
            await self._savepoint.close(rollback=True)
            self._savepoint = None
        self.postrollback.run()

    def __getattr__(self, name):
        """Delegate to the underlying cursor"""
        return getattr(self._cursor, name)

    async def now(self):
        """Return the transaction's timestamp"""
        if self._now is None:
            self._now = datetime.now()
        return self._now


class AsyncConnectionPool:
    """
    Async connection pool for PostgreSQL databases using asyncpg.
    Manages a pool of connections and provides async connection borrowing/returning.
    """

    def __init__(self, maxconn=64, readonly=False):
        self._pool = None
        self._maxconn = max(maxconn, 1)
        self._readonly = readonly
        self._lock = asyncio.Lock()
        self._connections = []  # List of [connection, used, last_used]

    def __repr__(self):
        used = len([1 for c, u, _ in self._connections[:] if u])
        count = len(self._connections)
        mode = 'read-only' if self._readonly else 'read/write'
        return f"AsyncConnectionPool({mode};used={used}/count={count}/max={self._maxconn})"

    @property
    def readonly(self):
        return self._readonly

    def _debug(self, msg, *args):
        _logger_conn.debug(('%r ' + msg), self, *args)

    async def borrow(self, connection_info):
        """
        Borrow an asyncpg connection from the pool. If no connection is available,
        create a new one as long as there are still slots available.
        """
        async with self._lock:
            # Clean up closed, idle, and leaked connections
            current_time = time.time()
            for i in reversed(range(len(self._connections))):
                cnx, used, last_used = self._connections[i]

                # Remove closed connections
                if cnx.is_closed():
                    self._connections.pop(i)
                    self._debug('Removing closed connection at index %d', i)
                    continue

                # Close idle connections
                if not used and current_time - last_used > MAX_IDLE_TIMEOUT:
                    await cnx.close()
                    self._connections.pop(i)
                    self._debug('Closed idle connection at index %d', i)
                    continue

                # Handle leaked connections
                if hasattr(cnx, 'leaked') and cnx.leaked:
                    delattr(cnx, 'leaked')
                    self._connections[i][1] = False
                    _logger.info('%r: Free leaked connection', self)

            # Try to find an available connection
            for i, (cnx, used, _) in enumerate(self._connections):
                if not used and not cnx.is_closed():
                    # Test connection is still valid
                    try:
                        await cnx.execute("SELECT 1")
                        self._connections[i][1] = True
                        self._debug('Borrow existing connection at index %d', i)
                        return cnx
                    except Exception:
                        # Connection is bad, remove it
                        await cnx.close()
                        self._connections.pop(i)
                        self._debug('Removed bad connection at index %d', i)
                        continue

            # If pool is full, try to remove oldest unused connection
            if len(self._connections) >= self._maxconn:
                for i, (cnx, used, _) in enumerate(self._connections):
                    if not used:
                        await cnx.close()
                        self._connections.pop(i)
                        self._debug('Removing old connection at index %d', i)
                        break
                else:
                    raise PoolError('The Connection Pool Is Full')

            # Create new connection
            try:
                # Convert connection_info to asyncpg format
                asyncpg_params = self._convert_connection_info(connection_info)
                cnx = await asyncpg.connect(**asyncpg_params)

                self._connections.append([cnx, True, current_time])
                self._debug('Create new async connection')
                return cnx

            except Exception as e:
                _logger.info('Connection to the database failed: %s', e)
                raise

    async def give_back(self, connection, keep_in_pool=True):
        """Return a connection to the pool"""
        async with self._lock:
            self._debug('Give back connection')
            for i, (cnx, _, _) in enumerate(self._connections):
                if cnx is connection:
                    if keep_in_pool and not cnx.is_closed():
                        # Release the connection and record the last time used
                        self._connections[i][1] = False
                        self._connections[i][2] = time.time()
                        self._debug('Put connection in pool')
                    else:
                        self._connections.pop(i)
                        self._debug('Forgot connection')
                        if not cnx.is_closed():
                            await cnx.close()
                    return
            raise PoolError('This connection does not belong to the pool')

    def _convert_connection_info(self, connection_info):
        """Convert psycopg2-style connection info to asyncpg format"""
        asyncpg_params = {}

        # Map common parameters
        param_mapping = {
            'host': 'host',
            'port': 'port',
            'user': 'user',
            'password': 'password',
            'database': 'database',
            'dbname': 'database',  # psycopg2 uses dbname, asyncpg uses database
        }

        for psycopg2_key, asyncpg_key in param_mapping.items():
            if psycopg2_key in connection_info:
                asyncpg_params[asyncpg_key] = connection_info[psycopg2_key]

        # Handle DSN format
        if 'dsn' in connection_info:
            # Parse DSN string and extract parameters
            dsn = connection_info['dsn']
            # This is a simplified DSN parser - you might need a more robust one
            if dsn.startswith('postgresql://') or dsn.startswith('postgres://'):
                # Use the DSN directly
                return {'dsn': dsn}

        return asyncpg_params

    async def close_all(self):
        """Close all connections in the pool"""
        async with self._lock:
            for cnx, _, _ in self._connections:
                if not cnx.is_closed():
                    await cnx.close()
            self._connections.clear()


class AsyncConnection:
    """A lightweight instance of an async connection to postgres"""

    def __init__(self, pool, dbname, dsn):
        self.__dbname = dbname
        self.__dsn = dsn
        self.__pool = pool

    @property
    def dsn(self):
        dsn = dict(self.__dsn)
        dsn.pop('password', None)
        return dsn

    @property
    def dbname(self):
        return self.__dbname

    def cursor(self):
        _logger.debug('create async cursor to %r', self.dsn)
        return AsyncCursor(self.__pool, self.__dbname, self.__dsn)

    def __bool__(self):
        raise NotImplementedError()


# Global connection pools
_AsyncPool = None
_AsyncPool_readonly = None


async def async_db_connect(to, allow_uri=False, readonly=False):
    """Create an async database connection"""
    global _AsyncPool, _AsyncPool_readonly

    maxconn = config.get('db_maxconn', 64)

    if _AsyncPool is None and not readonly:
        _AsyncPool = AsyncConnectionPool(int(maxconn), readonly=False)
    if _AsyncPool_readonly is None and readonly:
        _AsyncPool_readonly = AsyncConnectionPool(int(maxconn), readonly=True)

    db, info = connection_info_for(to, readonly)
    if not allow_uri and db != to:
        raise ValueError('URI connections not allowed')

    return AsyncConnection(_AsyncPool_readonly if readonly else _AsyncPool, db, info)


def connection_info_for(db_or_uri, readonly=False):
    """Parse the given db_or_uri and return a 2-tuple (dbname, connection_params)"""
    if db_or_uri.startswith(('postgresql://', 'postgres://')):
        # URI format
        parsed = urls.url_parse(db_or_uri)
        return parsed.path.lstrip('/'), {'dsn': db_or_uri}
    else:
        # Database name format - build connection params from config
        connection_params = {
            'database': db_or_uri,
            'user': config.get('db_user') or os.environ.get('USER'),
            'password': config.get('db_password') or False,
            'host': config.get('db_host') or False,
            'port': config.get('db_port') or 5432,
        }

        # Remove False values
        connection_params = {k: v for k, v in connection_params.items() if v is not False}

        return db_or_uri, connection_params
