# ASGI Conversion Summary

## Overview
This document summarizes the conversion of the Odoo server from WSGI to ASGI, including the migration from psycopg2 to asyncpg for async database operations.

## Changes Made

### 1. Dependencies Updated (`requirements.txt`)
- **Removed**: `psycopg2` (replaced with asyncpg)
- **Removed**: `gevent` and `greenlet` (incompatible with ASGI async model)
- **Added**: 
  - `asyncpg==0.29.0` - Async PostgreSQL driver
  - `uvicorn==0.30.6` - ASGI server
  - `uvloop==0.19.0` - Fast event loop (Linux/macOS only)
  - `asgiref==3.8.1` - ASGI utilities
  - `httptools==0.6.1` - Fast HTTP parsing
  - `websockets==12.0` - WebSocket support

### 2. Async Database Layer (`odoo/async_sql_db.py`)
- **AsyncCursor**: Async version of database cursor with asyncpg backend
- **AsyncConnectionPool**: Connection pooling for async database connections
- **AsyncTestCursor**: Test cursor for async operations
- **Compatibility**: Maintains same API as sync version where possible
- **Features**:
  - Async context managers
  - Savepoint support
  - Transaction management
  - Connection pooling with idle timeout
  - Parameter conversion from psycopg2 to asyncpg format

### 3. ASGI HTTP Layer (`odoo/asgi_http.py`)
- **ASGIApplication**: Main ASGI application replacing WSGI
- **ASGIRequest**: Request wrapper for ASGI scope/receive/send
- **AsyncHttpDispatcher**: Async HTTP request dispatcher
- **AsyncJsonRPCDispatcher**: Async JSON-RPC request dispatcher
- **Features**:
  - ASGI 3.0 compatible
  - Async request/response handling
  - Static file serving
  - Error handling
  - CSRF protection
  - Session management

### 4. Async Registry Support (`odoo/modules/async_registry.py`)
- **AsyncRegistryMixin**: Adds async capabilities to Registry
- **AsyncEnvironment**: Async-compatible environment wrapper
- **Features**:
  - Async cursor management
  - Connection pooling
  - Context managers
  - Compatibility with existing Registry API

### 5. Async Service Layer (`odoo/service/async_db.py`)
- **AsyncDatabaseService**: Async database operations
- **Functions**:
  - `async_create_database()` - Async database creation
  - `async_drop_database()` - Async database deletion
  - `async_duplicate_database()` - Async database duplication
  - `async_rename_database()` - Async database renaming
- **Compatibility**: Sync wrappers for backward compatibility

### 6. Server Implementation Updates (`odoo/service/server.py`)
- **ASGIServer**: New server class using uvicorn
- **Features**:
  - Uvicorn integration
  - Uvloop support for better performance
  - Signal handling
  - Graceful shutdown
  - Thread-based ASGI server startup
- **Auto-detection**: Automatically chooses ASGI when dependencies available

### 7. Configuration Updates (`odoo/tools/config.py`)
- **New Options**:
  - `--asgi` - Enable ASGI mode
  - `--server-mode` - Choose server mode (wsgi/asgi/auto)
  - `--asgi-workers` - Number of ASGI workers
  - `--asgi-loop` - Event loop implementation

### 8. CLI and Runner Updates
- **odoo_runner.py**: Added ASGI mode detection and status display
- **Server startup**: Auto-detection of ASGI mode based on dependencies

### 9. Compatibility Layer (`odoo/sql_db.py`, `odoo/http.py`)
- **Backward compatibility**: Existing sync code continues to work
- **Auto-detection**: Automatically uses async when ASGI mode enabled
- **Fallback**: Falls back to sync when async dependencies unavailable

### 10. Testing and Validation
- **Test suite**: `odoo/tests/test_asgi.py` - Comprehensive ASGI tests
- **Validation script**: `validate_asgi_conversion.py` - Conversion validation

## Usage

### Starting in ASGI Mode
```bash
# Method 1: Use --asgi flag
python odoo-bin --asgi

# Method 2: Use --server-mode
python odoo-bin --server-mode=asgi

# Method 3: Use odoo_runner.py
python odoo_runner.py --asgi
```

### Configuration Options
```bash
# Full ASGI configuration
python odoo-bin --asgi \
  --asgi-workers=4 \
  --asgi-loop=uvloop \
  --http-port=8069
```

### Environment Variables
```bash
# Enable ASGI mode via config
export ODOO_ASGI_ENABLE=1
export ODOO_SERVER_MODE=asgi
```

## Performance Benefits

### ASGI Advantages
1. **Better Concurrency**: Async I/O allows handling more concurrent requests
2. **Resource Efficiency**: Lower memory usage per connection
3. **Scalability**: Better performance under high load
4. **Modern Standards**: ASGI is the future of Python web applications

### Database Performance
1. **Async Queries**: Non-blocking database operations
2. **Connection Pooling**: Efficient connection management
3. **Reduced Latency**: Faster response times for I/O-bound operations

## Compatibility

### Backward Compatibility
- All existing WSGI code continues to work
- Existing decorators and middleware are preserved
- Database API remains the same for sync operations
- Configuration files are backward compatible

### Migration Path
1. **Phase 1**: Install ASGI dependencies
2. **Phase 2**: Test with `--asgi` flag
3. **Phase 3**: Gradually migrate custom code to async
4. **Phase 4**: Full ASGI deployment

## Dependencies

### Required for ASGI
- `asyncpg>=0.29.0` - Async PostgreSQL driver
- `uvicorn>=0.30.0` - ASGI server
- `asgiref>=3.8.0` - ASGI utilities

### Optional for Performance
- `uvloop>=0.19.0` - Fast event loop (Linux/macOS)
- `httptools>=0.6.0` - Fast HTTP parsing
- `websockets>=12.0` - WebSocket support

### Installation
```bash
# Install ASGI dependencies
pip install asyncpg uvicorn asgiref

# Install optional performance packages
pip install uvloop httptools websockets
```

## Validation

Run the validation script to check the conversion:
```bash
python validate_asgi_conversion.py
```

Run the test suite:
```bash
python -m pytest odoo/tests/test_asgi.py -v
```

## Troubleshooting

### Common Issues
1. **Import Errors**: Ensure all ASGI dependencies are installed
2. **Database Errors**: Check asyncpg connection parameters
3. **Performance Issues**: Enable uvloop for better performance
4. **Compatibility**: Some third-party modules may need updates

### Fallback to WSGI
If ASGI mode fails, the system automatically falls back to WSGI mode with appropriate warnings.

## Future Enhancements

### Planned Improvements
1. **WebSocket Support**: Real-time communication
2. **HTTP/2 Support**: Better performance
3. **Async ORM**: Fully async database operations
4. **Streaming Responses**: Large file handling
5. **Background Tasks**: Async task processing

### Migration Recommendations
1. Start with ASGI in development
2. Test thoroughly before production
3. Monitor performance metrics
4. Gradually migrate custom modules
5. Keep WSGI as fallback option

## Conclusion

The ASGI conversion provides a modern, scalable foundation for Odoo while maintaining full backward compatibility. The async database layer offers significant performance improvements for I/O-bound operations, and the modular design allows for gradual migration.

The conversion is complete and ready for testing and deployment.
