# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Tests for ASGI functionality and async database operations.
"""
import asyncio
import unittest
from unittest.mock import patch, MagicMock

from odoo.tests.common import BaseCase


class TestASGISupport(BaseCase):
    """Test ASGI support and async functionality"""

    def test_asgi_dependencies_check(self):
        """Test ASGI dependencies availability check"""
        from odoo.http import Application
        app = Application()
        
        # Test with mocked dependencies
        with patch('odoo.http.import_module') as mock_import:
            # Mock successful import
            mock_import.return_value = MagicMock()
            self.assertTrue(app.is_asgi_supported())
            
            # Mock failed import
            mock_import.side_effect = ImportError("Module not found")
            self.assertFalse(app.is_asgi_supported())

    def test_async_database_imports(self):
        """Test async database module imports"""
        try:
            from odoo.async_sql_db import AsyncCursor, AsyncConnectionPool
            self.assertTrue(True, "Async database modules imported successfully")
        except ImportError:
            self.skipTest("Async database dependencies not available")

    def test_async_registry_support(self):
        """Test async registry functionality"""
        try:
            from odoo.modules.async_registry import patch_registry_for_async, AsyncEnvironment
            self.assertTrue(True, "Async registry modules imported successfully")
        except ImportError:
            self.skipTest("Async registry dependencies not available")

    def test_asgi_http_imports(self):
        """Test ASGI HTTP module imports"""
        try:
            from odoo.asgi_http import ASGIApplication, ASGIRequest
            self.assertTrue(True, "ASGI HTTP modules imported successfully")
        except ImportError:
            self.skipTest("ASGI HTTP dependencies not available")

    def test_async_service_imports(self):
        """Test async service module imports"""
        try:
            from odoo.service.async_db import AsyncDatabaseService
            self.assertTrue(True, "Async service modules imported successfully")
        except ImportError:
            self.skipTest("Async service dependencies not available")

    def test_config_asgi_options(self):
        """Test ASGI configuration options"""
        from odoo.tools import config
        
        # Test default values
        self.assertFalse(config.get('asgi_enable', False))
        self.assertEqual(config.get('server_mode', 'wsgi'), 'wsgi')
        self.assertEqual(config.get('asgi_workers', 1), 1)
        self.assertEqual(config.get('asgi_loop', 'auto'), 'auto')

    @unittest.skipUnless(
        hasattr(asyncio, 'run'), 
        "asyncio.run not available (Python < 3.7)"
    )
    def test_async_cursor_basic_functionality(self):
        """Test basic async cursor functionality"""
        try:
            from odoo.async_sql_db import AsyncCursor
            
            # Mock connection pool and database
            mock_pool = MagicMock()
            mock_connection = MagicMock()
            mock_pool.borrow.return_value = asyncio.coroutine(lambda: mock_connection)()
            
            cursor = AsyncCursor(mock_pool, 'test_db', {'host': 'localhost'})
            
            # Test cursor attributes
            self.assertEqual(cursor.dbname, 'test_db')
            self.assertFalse(cursor.closed)
            
        except ImportError:
            self.skipTest("Async database dependencies not available")

    def test_server_mode_detection(self):
        """Test server mode detection in odoo_runner"""
        try:
            import sys
            import os
            
            # Add the project root to sys.path to import odoo_runner
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            from odoo_runner import OdooRunner
            
            # Test ASGI mode detection
            runner_asgi = OdooRunner(['--asgi'])
            self.assertTrue(runner_asgi.asgi_mode)
            
            runner_server_mode = OdooRunner(['--server-mode=asgi'])
            self.assertTrue(runner_server_mode.asgi_mode)
            
            runner_wsgi = OdooRunner(['--server-mode=wsgi'])
            self.assertFalse(runner_wsgi.asgi_mode)
            
        except ImportError:
            self.skipTest("odoo_runner not available")

    def test_async_compatibility_layer(self):
        """Test async/sync compatibility layer"""
        from odoo.sql_db import db_connect
        
        # Test that db_connect still works (should fall back to sync)
        with patch('odoo.tools.config') as mock_config:
            mock_config.get.return_value = False
            
            # This should not raise an exception
            try:
                # We can't actually connect without a real database
                # but we can test that the function exists and handles config
                self.assertTrue(callable(db_connect))
            except Exception:
                # Expected if no database is available
                pass

    def test_asgi_application_creation(self):
        """Test ASGI application creation"""
        try:
            from odoo.asgi_http import ASGIApplication
            
            app = ASGIApplication()
            self.assertIsNotNone(app)
            self.assertTrue(hasattr(app, '__call__'))
            self.assertTrue(hasattr(app, 'statics'))
            
        except ImportError:
            self.skipTest("ASGI dependencies not available")

    def test_async_request_creation(self):
        """Test async request creation"""
        try:
            from odoo.asgi_http import ASGIRequest
            
            # Mock ASGI scope
            scope = {
                'type': 'http',
                'method': 'GET',
                'path': '/test',
                'query_string': b'',
                'headers': [],
                'server': ('localhost', 8069),
                'scheme': 'http',
            }
            
            receive = MagicMock()
            send = MagicMock()
            
            request = ASGIRequest(scope, receive, send)
            self.assertIsNotNone(request)
            self.assertEqual(request.scope, scope)
            
        except ImportError:
            self.skipTest("ASGI dependencies not available")


class TestAsyncDatabaseOperations(BaseCase):
    """Test async database operations"""

    def test_async_cursor_context_manager(self):
        """Test async cursor as context manager"""
        try:
            from odoo.async_sql_db import AsyncCursor
            
            # Mock the necessary components
            mock_pool = MagicMock()
            cursor = AsyncCursor(mock_pool, 'test_db', {})
            
            # Test that it has the required async context manager methods
            self.assertTrue(hasattr(cursor, '__aenter__'))
            self.assertTrue(hasattr(cursor, '__aexit__'))
            
        except ImportError:
            self.skipTest("Async database dependencies not available")

    def test_async_savepoint(self):
        """Test async savepoint functionality"""
        try:
            from odoo.async_sql_db import Savepoint
            
            # Mock cursor
            mock_cursor = MagicMock()
            mock_cursor.execute = MagicMock(return_value=asyncio.coroutine(lambda: None)())
            
            savepoint = Savepoint(mock_cursor)
            self.assertIsNotNone(savepoint)
            self.assertFalse(savepoint._closed)
            
        except ImportError:
            self.skipTest("Async database dependencies not available")


class TestAsyncEnvironment(BaseCase):
    """Test async environment functionality"""

    def test_async_environment_creation(self):
        """Test async environment creation"""
        try:
            from odoo.modules.async_registry import AsyncEnvironment
            
            # Mock components
            mock_cursor = MagicMock()
            mock_registry = MagicMock()
            
            env = AsyncEnvironment(mock_cursor, 1, {}, mock_registry)
            self.assertIsNotNone(env)
            self.assertEqual(env.uid, 1)
            self.assertEqual(env.context, {})
            
        except ImportError:
            self.skipTest("Async environment dependencies not available")


if __name__ == '__main__':
    unittest.main()
