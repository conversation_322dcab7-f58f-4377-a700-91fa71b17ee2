#!/usr/bin/env python3
"""
Odoo Runner Script with Interactive Controls

This script runs odoo-bin with interactive controls for:
- Restart (r)
- Exit (x)
- Status (s)
- Force kill (k)
- Help (h)

Usage: python odoo_runner.py [odoo-bin arguments]
"""

import os
import sys
import subprocess
import signal
import threading
import time
from typing import Optional, List

# Platform-specific imports
try:
    import select
    import termios
    import tty
    UNIX_LIKE = True
except ImportError:
    # Windows doesn't have these modules
    UNIX_LIKE = False
    try:
        import msvcrt
    except ImportError:
        msvcrt = None


class OdooRunner:
    def __init__(self, odoo_args: List[str] = None):
        self.odoo_args = odoo_args or []
        self.process: Optional[subprocess.Popen] = None
        self.running = False
        self.should_restart = False

        # Check if ASGI mode is requested
        self.asgi_mode = '--asgi' in self.odoo_args or any(arg.startswith('--server-mode=asgi') for arg in self.odoo_args)

        self.original_terminal_settings = None
    def setup_terminal(self):
        """Setup terminal for non-blocking input"""
        if UNIX_LIKE:
            try:
                self.original_terminal_settings = termios.tcgetattr(sys.stdin)
                tty.setraw(sys.stdin.fileno())
            except (termios.error, OSError):
                # Fallback for environments where terminal control isn't available
                self.original_terminal_settings = None
        else:
            # Windows doesn't need special setup
            self.original_terminal_settings = None

    def restore_terminal(self):
        """Restore original terminal settings"""
        if UNIX_LIKE and self.original_terminal_settings:
            try:
                termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.original_terminal_settings)
            except (termios.error, OSError):
                pass

    def get_char_non_blocking(self) -> Optional[str]:
        """Get a character from stdin without blocking"""
        if UNIX_LIKE:
            try:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    return sys.stdin.read(1)
            except (OSError, ValueError):
                pass
        else:
            # Windows implementation using msvcrt
            if msvcrt and msvcrt.kbhit():
                return msvcrt.getch().decode('utf-8', errors='ignore')
        return None

    def start_odoo(self) -> bool:
        """Start the Odoo process"""
        try:
            odoo_bin_path = os.path.join(os.getcwd(), 'odoo-bin')
            if not os.path.exists(odoo_bin_path):
                print(f"❌ Error: odoo-bin not found at {odoo_bin_path}")
                return False

            cmd = [sys.executable, odoo_bin_path] + self.odoo_args
            mode_info = " (ASGI mode)" if self.asgi_mode else " (WSGI mode)"
            print(f"🚀 Starting Odoo{mode_info}: {' '.join(cmd)}")

            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            self.running = True
            return True

        except Exception as e:
            print(f"❌ Error starting Odoo: {e}")
            return False

    def stop_odoo(self, force: bool = False) -> bool:
        """Stop the Odoo process"""
        if not self.process:
            return True

        try:
            if force:
                print("🔥 Force killing Odoo process...")
                self.process.kill()
            else:
                print("🛑 Stopping Odoo gracefully...")
                self.process.terminate()

            # Wait for process to exit
            try:
                self.process.wait(timeout=10)
                print("✅ Odoo stopped successfully")
            except subprocess.TimeoutExpired:
                print("⚠️  Timeout waiting for graceful shutdown, force killing...")
                self.process.kill()
                self.process.wait()
                print("✅ Odoo force killed")

            self.running = False
            return True

        except Exception as e:
            print(f"❌ Error stopping Odoo: {e}")
            return False

    def restart_odoo(self):
        """Restart the Odoo process"""
        print("🔄 Restarting Odoo...")
        self.stop_odoo()
        time.sleep(2)  # Give it a moment
        self.start_odoo()

    def get_status(self):
        """Get current status of Odoo process"""
        if not self.process:
            return "❌ Not running"

        poll = self.process.poll()
        if poll is None:
            return f"✅ Running (PID: {self.process.pid})"
        else:
            return f"❌ Stopped (Exit code: {poll})"

    def print_help(self):
        """Print available commands"""
        print("\n" + "="*50)
        print("📋 ODOO RUNNER CONTROLS")
        print("="*50)
        print("r - Restart Odoo")
        print("x - Exit runner")
        print("s - Show status")
        print("k - Force kill Odoo")
        print("h - Show this help")
        print("Ctrl+C - Graceful shutdown")
        print("="*50 + "\n")

    def output_reader(self):
        """Read and display Odoo output in a separate thread"""
        if not self.process:
            return

        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    print(line.rstrip())
                if self.process.poll() is not None:
                    break
        except Exception as e:
            print(f"❌ Error reading output: {e}")

    def run(self):
        """Main run loop"""
        print("🎯 Odoo Runner Started")
        self.print_help()

        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        try:
            self.setup_terminal()

            # Start Odoo
            if not self.start_odoo():
                return 1

            # Start output reader thread
            output_thread = threading.Thread(target=self.output_reader, daemon=True)
            output_thread.start()

            # Main control loop
            while self.running:
                char = self.get_char_non_blocking()

                if char:
                    if char.lower() == 'r':
                        self.restart_odoo()
                        # Restart output reader
                        output_thread = threading.Thread(target=self.output_reader, daemon=True)
                        output_thread.start()
                    elif char.lower() == 'x':
                        print("\n👋 Exiting...")
                        break
                    elif char.lower() == 's':
                        print(f"\n📊 Status: {self.get_status()}")
                    elif char.lower() == 'k':
                        self.stop_odoo(force=True)
                    elif char.lower() == 'h':
                        self.print_help()

                # Check if process is still running
                if self.process and self.process.poll() is not None:
                    print(f"\n⚠️  Odoo process exited with code: {self.process.poll()}")
                    if self.should_restart:
                        self.restart_odoo()
                        self.should_restart = False
                    else:
                        self.running = False

                time.sleep(0.1)  # Small delay to prevent high CPU usage

            # Clean shutdown
            self.stop_odoo()

        except KeyboardInterrupt:
            print("\n🛑 Ctrl+C received, shutting down...")
            self.stop_odoo()
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            self.stop_odoo()
        finally:
            self.restore_terminal()
            print("👋 Goodbye!")

        return 0

    def signal_handler(self, signum, frame):
        """Handle system signals"""
        _ = frame  # Unused parameter
        print(f"\n📡 Received signal {signum}")
        self.running = False


def main():
    """Main entry point"""
    # Parse command line arguments for odoo-bin
    odoo_args = sys.argv[1:] if len(sys.argv) > 1 else []

    if '--help' in odoo_args or '-h' in odoo_args:
        print(__doc__)
        return 0

    runner = OdooRunner(odoo_args)
    return runner.run()


if __name__ == "__main__":
    sys.exit(main())
