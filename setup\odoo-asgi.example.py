#!/usr/bin/env python3
"""
ASGI config for Odoo.

This module contains the ASGI application used by Odoo.

For more information on this file, see
https://docs.python.org/3/library/asyncio.html

Example usage with uvicorn:
    uvicorn setup.odoo-asgi:application --host 0.0.0.0 --port 8069

Example usage with gunicorn:
    gunicorn setup.odoo-asgi:application -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8069
"""

import odoo

# Equivalent of --load command-line option
odoo.conf.server_wide_modules = ['base', 'web']
conf = odoo.tools.config

# Path to the OpenERP Addons repository (comma-separated for
# multiple locations)
#conf['addons_path'] = './odoo/addons,./addons'

# Optional database config if not using local socket
#conf['db_name'] = 'mycompany'
#conf['db_host'] = 'localhost'
#conf['db_user'] = 'foo'
#conf['db_port'] = 5432
#conf['db_password'] = 'secret'

# Enable ASGI mode
conf['asgi_enable'] = True
conf['server_mode'] = 'asgi'

#----------------------------------------------------------
# ASGI application
#----------------------------------------------------------
application = odoo.http.root.asgi_call

odoo.service.server.load_server_wide_modules()
