# The officially supported versions of the following packages are their
# python3-* equivalent distributed in Ubuntu 24.04 and Debian 12
# ASGI and Async Database Dependencies
asyncpg==0.29.0 ; python_version >= '3.10'  # Async PostgreSQL driver
uvicorn==0.30.6 ; python_version >= '3.10'  # ASGI server
uvloop==0.19.0 ; python_version >= '3.10' and sys_platform != 'win32'  # Fast event loop
asgiref==3.8.1 ; python_version >= '3.10'  # ASGI utilities
httptools==0.6.1 ; python_version >= '3.10'  # Fast HTTP parsing
websockets==12.0 ; python_version >= '3.10'  # WebSocket support
asn1crypto==1.4.0 ; python_version < '3.11'
asn1crypto==1.5.1 ; python_version >= '3.11'
Babel==2.9.1 ; python_version < '3.11'  # min version = 2.6.0 (Focal with security backports)
Babel==2.10.3 ; python_version >= '3.11' and python_version < '3.13'
Babel==2.17.0 ; python_version >= '3.13'
cbor2==5.4.2 ; python_version < '3.12'
cbor2==5.6.2 ; python_version >= '3.12'
chardet==4.0.0 ; python_version < '3.11'  # (Jammy)
chardet==5.2.0 ; python_version >= '3.11'
cryptography==3.4.8; python_version < '3.12'  # incompatibility between pyopenssl 19.0.0 and cryptography>=37.0.0
cryptography==42.0.8 ; python_version >= '3.12'  # (Noble) min 41.0.7, pinning 42.0.8 for security fixes
decorator==4.4.2  ; python_version < '3.11'  # (Jammy)
decorator==5.1.1  ; python_version >= '3.11'
docutils==0.17 ; python_version < '3.11'  # (Jammy)
docutils==0.20.1 ; python_version >= '3.11'
freezegun==1.1.0 ; python_version < '3.11'  # (Jammy)
freezegun==1.2.1 ; python_version >= '3.11' and python_version < '3.13'
freezegun==1.5.1 ; python_version >= '3.13'
geoip2==2.9.0
# Note: gevent and greenlet removed as they're not compatible with ASGI async model
idna==2.10 ; python_version < '3.12'  # requests 2.25.1 depends on idna<3 and >=2.5
idna==3.6 ; python_version >= '3.12'
Jinja2==3.0.3 ; python_version <= '3.10'
Jinja2==3.1.2 ; python_version > '3.10'
libsass==0.20.1 ; python_version < '3.11'
libsass==0.22.0 ; python_version >= '3.11'  # (Noble) Mostly to have a wheel package
lxml==4.8.0 ; python_version <= '3.10'
lxml==4.9.3 ; python_version > '3.10' and python_version < '3.12' # min 4.9.2, pinning 4.9.3 because of missing wheels for darwin in 4.9.3
lxml==5.2.1; python_version >= '3.12' # (Noble - removed html clean)
lxml-html-clean; python_version >= '3.12' # (Noble - removed from lxml, unpinned for futur security patches)
MarkupSafe==2.0.1 ; python_version <= '3.10'
MarkupSafe==2.1.2 ; python_version > '3.10' and python_version < '3.12'
MarkupSafe==2.1.5 ; python_version >= '3.12'  # (Noble) Mostly to have a wheel package
num2words==0.5.10 ; python_version < '3.12'  # (Jammy / Bookworm)
num2words==0.5.13 ; python_version >= '3.12'
ofxparse==0.21
openpyxl==3.0.9 ; python_version < '3.12'
openpyxl==3.1.2 ; python_version >= '3.12'
passlib==1.7.4 # min version = 1.7.2 (Focal with security backports)
Pillow==9.0.1 ; python_version <= '3.10'
Pillow==9.4.0 ; python_version > '3.10' and python_version < '3.12'
Pillow==10.2.0 ; python_version >= '3.12' and python_version < '3.13'  # (Noble) Mostly to have a wheel package
Pillow==11.1.0 ; python_version >= '3.13'  # (Noble) Mostly to have a wheel package
polib==1.1.1
psutil==5.9.0 ; python_version <= '3.10'
psutil==5.9.4 ; python_version > '3.10' and python_version < '3.12'
psutil==5.9.8 ; python_version >= '3.12' # (Noble) Mostly to have a wheel package
# psycopg2 replaced with asyncpg for async database operations
pyopenssl==21.0.0 ; python_version < '3.12'
pyopenssl==24.1.0 ; python_version >= '3.12' # (Noble) min 23.2.0, pinned for compatibility with cryptography==42.0.8 and security patches
PyPDF2==1.26.0 ; python_version <= '3.10'
PyPDF2==2.12.1 ; python_version > '3.10'
pypiwin32 ; sys_platform == 'win32'
pyserial==3.5
python-dateutil==2.8.1 ; python_version < '3.11'
python-dateutil==2.8.2 ; python_version >= '3.11'
python-ldap==3.4.0 ; sys_platform != 'win32' and python_version < '3.12' # min version = 3.2.0 (Focal with security backports)
python-ldap==3.4.4 ; sys_platform != 'win32' and python_version >= '3.12'  # (Noble) Mostly to have a wheel package
python-stdnum==1.17 ; python_version < '3.11'  # (jammy)
python-stdnum==1.19 ; python_version >= '3.11'
pytz  # no version pinning to avoid OS perturbations
pyusb==1.2.1
qrcode==7.3.1 ; python_version < '3.11'  # (jammy)
qrcode==7.4.2 ; python_version >= '3.11'
reportlab==3.6.8 ; python_version <= '3.10'
reportlab==3.6.12 ; python_version > '3.10' and python_version < '3.12'
reportlab==4.1.0 ; python_version >= '3.12' # (Noble) Mostly to have a wheel package
requests==2.25.1 ;  python_version < '3.11' # versions < 2.25 aren't compatible w/ urllib3 1.26. Bullseye = 2.25.1. min version = 2.22.0 (Focal)
requests==2.31.0 ; python_version >= '3.11' # (Noble)
rjsmin==1.1.0 ; python_version < '3.11'  # (jammy)
rjsmin==1.2.0 ; python_version >= '3.11'
rl-renderPM==4.0.3 ; sys_platform == 'win32' and python_version >= '3.12'  # Needed by reportlab 4.1.0 but included in deb package
urllib3==1.26.5 ; python_version < '3.12' # indirect / min version = 1.25.8 (Focal with security backports)
urllib3==2.0.7  ; python_version >= '3.12'  # (Noble) Compatibility with cryptography
vobject==0.9.6.1
# Werkzeug removed - using ASGI server (uvicorn) instead of WSGI
xlrd==1.2.0 ; python_version < '3.12'  # (jammy)
xlrd==2.0.1 ; python_version >= '3.12'
XlsxWriter==3.0.2 ; python_version < '3.12'  # (jammy)
XlsxWriter==3.1.9 ; python_version >= '3.12'
xlwt==1.3.0
zeep==4.1.0 ; python_version < '3.11'  # (jammy)
zeep==4.2.1 ; python_version >= '3.11' and python_version < '3.13'
zeep==4.3.1 ; python_version >= '3.13'
